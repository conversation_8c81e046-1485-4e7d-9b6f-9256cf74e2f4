from datetime import datetime, time, date
from zoneinfo import ZoneInfo
import pandas_market_calendars as mcal

class Clock:
    tz = ZoneInfo("America/New_York")
    
    def __init__(self, replay_time: datetime = None):
        """
        If replay_time is provided, the clock will always return that value
        (unless updated via set()). Otherwise, it defaults to live mode, returning
        the current EST time.
        """
        self.replay_time = replay_time

    def now(self) -> datetime:
        """
        Returns the current time. In replay mode, returns the fixed replay_time.
        In live mode, returns the current datetime in EST.
        """
        if self.replay_time is not None:
            return self.replay_time
        return datetime.now(ZoneInfo("America/New_York"))

    def set(self, new_time: datetime):
        """
        Updates the replay time.
        """
        self.replay_time = new_time 
        
def is_trading_time(dt: datetime) -> bool:
    """
    Check if a datetime (dt) falls within allowed trading hours in EST.
    Tradable sessions in EST:
      - Pre-market: 4:00 AM to 9:30 AM
      - Regular hours: 9:30 AM to 4:00 PM
      - After-hours: 4:00 PM to 8:00 PM
    """
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=ZoneInfo("America/New_York"))
    t = dt.time()
    return (time(4, 0) <= t < time(9, 30)) or (time(9, 30) <= t < time(16, 0)) or (time(16, 0) <= t < time(20, 0))

def is_regular_hours(dt: datetime) -> bool:
    """
    Check if a datetime (dt) falls within regular trading hours (9:30–16:00 EST).
    """
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=ZoneInfo("America/New_York"))
    return time(9, 30) <= dt.time() < time(16, 0)

_nyse_calendar = None

def _get_nyse_calendar():
    """Lazy initializes and returns the NYSE market calendar."""
    global _nyse_calendar
    if _nyse_calendar is None:
        _nyse_calendar = mcal.get_calendar('NYSE')
    return _nyse_calendar

def is_trading_day(dt: datetime | date) -> bool:
    """
    Check if a given date is a trading day on the NYSE.
    Uses pandas_market_calendars.
    """
    if isinstance(dt, datetime):
        target_date = dt.date()
    else:
        target_date = dt
        
    nyse = _get_nyse_calendar()
    # Get schedule for the year to check against
    schedule = nyse.schedule(
        start_date=date(target_date.year, 1, 1),
        end_date=date(target_date.year, 12, 31),
    )
    # Directly compare the Python date to the dates in the schedule index
    return target_date in schedule.index.date

def get_session_bounds(day_dt: datetime) -> tuple[datetime, datetime]:
    """
    Get the market session bounds (open and close times) for a given day.

    Args:
        day_dt: A datetime representing the trading day

    Returns:
        tuple: (session_open, session_close) as timezone-aware datetime objects in EST
    """
    open_t = time(9, 30)  # 9:30 AM
    close_t = time(15, 55)  # 3:55 PM (close before market close for safety)

    est_tz = ZoneInfo("America/New_York")

    # Ensure day_dt is timezone-aware
    if day_dt.tzinfo is None:
        day_dt = day_dt.replace(tzinfo=est_tz)
    else:
        day_dt = day_dt.astimezone(est_tz)

    session_open = datetime.combine(day_dt.date(), open_t, tzinfo=est_tz)
    session_close = datetime.combine(day_dt.date(), close_t, tzinfo=est_tz)

    return (session_open, session_close)